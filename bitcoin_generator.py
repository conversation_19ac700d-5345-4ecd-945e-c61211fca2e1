import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import hashlib
import base58
import secrets
import ecdsa
from ecdsa import Signing<PERSON>ey, SECP256k1


class BitcoinKeyGenerator:
    def __init__(self, root):
        self.root = root
        self.root.title("比特币密钥和地址生成器")
        self.root.geometry("600x500")
        self.root.resizable(True, True)
        
        # 创建主框架
        main_frame = ttk.Frame(root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        root.columnconfigure(0, weight=1)
        root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 客户类型
        ttk.Label(main_frame, text="客户类型:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.client_type = ttk.Combobox(main_frame, values=["wif", "hex"], state="readonly", width=20)
        self.client_type.set("wif")
        self.client_type.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        
        # 地址类型
        ttk.Label(main_frame, text="地址类型:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.address_type = ttk.Combobox(main_frame, values=["p2pkh", "p2sh", "bech32"], state="readonly", width=20)
        self.address_type.set("p2pkh")
        self.address_type.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        
        # 生成数量
        ttk.Label(main_frame, text="生成数量:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.count_var = tk.StringVar(value="1")
        count_entry = ttk.Entry(main_frame, textvariable=self.count_var, width=20)
        count_entry.grid(row=2, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, columnspan=2, pady=20)
        
        # 生成按钮
        generate_btn = ttk.Button(button_frame, text="生成密钥和地址", command=self.generate_keys)
        generate_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 清空按钮
        clear_btn = ttk.Button(button_frame, text="清空编辑", command=self.clear_results)
        clear_btn.pack(side=tk.LEFT)
        
        # 结果显示区域
        result_frame = ttk.LabelFrame(main_frame, text="生成结果:", padding="5")
        result_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        result_frame.columnconfigure(0, weight=1)
        result_frame.rowconfigure(0, weight=1)
        
        # 文本显示区域
        self.result_text = scrolledtext.ScrolledText(result_frame, height=15, width=70)
        self.result_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置主框架的行权重
        main_frame.rowconfigure(4, weight=1)
        
        # 状态标签
        self.status_label = ttk.Label(main_frame, text="完成！生成了 0 个密钥和地址")
        self.status_label.grid(row=5, column=0, columnspan=2, pady=(10, 0))

    def generate_private_key(self):
        """生成32字节的私钥"""
        return secrets.randbits(256).to_bytes(32, 'big')

    def private_key_to_wif(self, private_key, compressed=True):
        """将私钥转换为WIF格式"""
        # 添加网络字节（主网为0x80）
        extended_key = b'\x80' + private_key
        
        # 如果是压缩格式，添加0x01
        if compressed:
            extended_key += b'\x01'
        
        # 计算校验和
        checksum = hashlib.sha256(hashlib.sha256(extended_key).digest()).digest()[:4]
        
        # 组合并编码
        wif_key = extended_key + checksum
        return base58.b58encode(wif_key).decode('utf-8')

    def private_key_to_public_key(self, private_key, compressed=True):
        """从私钥生成公钥"""
        sk = SigningKey.from_string(private_key, curve=SECP256k1)
        vk = sk.get_verifying_key()
        
        if compressed:
            # 压缩公钥格式
            x = vk.pubkey.point.x()
            y = vk.pubkey.point.y()
            prefix = b'\x02' if y % 2 == 0 else b'\x03'
            return prefix + x.to_bytes(32, 'big')
        else:
            # 未压缩公钥格式
            return b'\x04' + vk.to_string()

    def public_key_to_address(self, public_key, address_type="p2pkh"):
        """从公钥生成地址"""
        if address_type == "p2pkh":
            return self.public_key_to_p2pkh(public_key)
        elif address_type == "p2sh":
            return self.public_key_to_p2sh(public_key)
        elif address_type == "bech32":
            return self.public_key_to_bech32(public_key)
        else:
            return "Unsupported address type"

    def public_key_to_p2pkh(self, public_key):
        """生成P2PKH地址"""
        # SHA256哈希
        sha256_hash = hashlib.sha256(public_key).digest()
        
        # RIPEMD160哈希
        ripemd160 = hashlib.new('ripemd160')
        ripemd160.update(sha256_hash)
        hash160 = ripemd160.digest()
        
        # 添加网络字节（主网P2PKH为0x00）
        versioned_hash = b'\x00' + hash160
        
        # 计算校验和
        checksum = hashlib.sha256(hashlib.sha256(versioned_hash).digest()).digest()[:4]
        
        # 组合并编码
        address_bytes = versioned_hash + checksum
        return base58.b58encode(address_bytes).decode('utf-8')

    def public_key_to_p2sh(self, public_key):
        """生成P2SH地址（简化版本）"""
        # 这里简化处理，实际P2SH需要脚本
        sha256_hash = hashlib.sha256(public_key).digest()
        ripemd160 = hashlib.new('ripemd160')
        ripemd160.update(sha256_hash)
        hash160 = ripemd160.digest()
        
        # P2SH网络字节为0x05
        versioned_hash = b'\x05' + hash160
        checksum = hashlib.sha256(hashlib.sha256(versioned_hash).digest()).digest()[:4]
        address_bytes = versioned_hash + checksum
        return base58.b58encode(address_bytes).decode('utf-8')

    def public_key_to_bech32(self, public_key):
        """生成Bech32地址（简化版本）"""
        # 这里返回一个示例格式，实际bech32编码更复杂
        sha256_hash = hashlib.sha256(public_key).digest()
        ripemd160 = hashlib.new('ripemd160')
        ripemd160.update(sha256_hash)
        hash160 = ripemd160.digest()
        
        # 简化的bech32格式
        return f"bc1q{hash160.hex()}"

    def generate_keys(self):
        """生成密钥和地址"""
        try:
            count = int(self.count_var.get())
            if count <= 0 or count > 1000:
                messagebox.showerror("错误", "生成数量必须在1-1000之间")
                return
        except ValueError:
            messagebox.showerror("错误", "请输入有效的数字")
            return
        
        client_type = self.client_type.get()
        address_type = self.address_type.get()
        
        results = []
        
        for i in range(count):
            # 生成私钥
            private_key = self.generate_private_key()
            
            # 根据客户类型格式化私钥
            if client_type == "wif":
                formatted_private_key = self.private_key_to_wif(private_key)
            else:  # hex
                formatted_private_key = private_key.hex()
            
            # 生成公钥
            public_key = self.private_key_to_public_key(private_key)
            
            # 生成地址
            address = self.public_key_to_address(public_key, address_type)
            
            results.append(f"密钥对 {i+1}:")
            results.append(f"私钥 ({client_type}): {formatted_private_key}")
            results.append(f"地址 ({address_type}): {address}")
            results.append("-" * 80)
        
        # 显示结果
        self.result_text.delete(1.0, tk.END)
        self.result_text.insert(tk.END, "\n".join(results))
        
        # 更新状态
        self.status_label.config(text=f"完成！生成了 {count} 个密钥和地址")

    def clear_results(self):
        """清空结果"""
        self.result_text.delete(1.0, tk.END)
        self.status_label.config(text="完成！生成了 0 个密钥和地址")


def main():
    root = tk.Tk()
    app = BitcoinKeyGenerator(root)
    root.mainloop()


if __name__ == "__main__":
    main()
