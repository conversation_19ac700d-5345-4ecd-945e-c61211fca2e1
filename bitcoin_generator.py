import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import hashlib
import base58
import secrets
import ecdsa
from ecdsa import SigningKey, SECP256k1
import threading
import time
import requests
import json
import logging
import os
from datetime import datetime
import pystray
from PIL import Image, ImageDraw
import queue


class BitcoinMiner:
    def __init__(self, root):
        self.root = root
        self.root.title("BitMiner")
        self.root.geometry("800x600")
        self.root.resizable(True, True)

        # 初始化变量
        self.is_running = False
        self.worker_thread = None
        self.generated_count = 0
        self.checked_count = 0
        self.found_count = 0
        self.start_time = None
        self.log_queue = queue.Queue()

        # 设置日志
        self.setup_logging()

        # 创建托盘图标
        self.setup_tray()

        # 绑定关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # 创建界面
        self.create_widgets()

        # 启动日志处理线程
        self.log_thread = threading.Thread(target=self.process_log_queue, daemon=True)
        self.log_thread.start()

    def setup_logging(self):
        """设置日志"""
        self.logger = logging.getLogger('BitMiner')
        self.logger.setLevel(logging.INFO)

        # 文件处理器
        file_handler = logging.FileHandler('log.txt', encoding='utf-8')
        file_handler.setLevel(logging.INFO)

        # 格式化器
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)

        self.logger.addHandler(file_handler)

    def create_tray_image(self):
        """创建托盘图标"""
        # 创建一个简单的图标
        image = Image.new('RGB', (64, 64), color='blue')
        draw = ImageDraw.Draw(image)
        draw.rectangle([16, 16, 48, 48], fill='white')
        draw.text((20, 25), "BM", fill='black')
        return image

    def setup_tray(self):
        """设置系统托盘"""
        try:
            image = self.create_tray_image()
            menu = pystray.Menu(
                pystray.MenuItem("显示程序", self.show_window),
                pystray.MenuItem("退出程序", self.quit_application)
            )

            self.tray_icon = pystray.Icon("BitMiner", image, "BitMiner", menu)
            self.tray_icon.run_detached()
        except Exception as e:
            print(f"托盘设置失败: {e}")

    def create_widgets(self):
        """创建界面组件"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.columnconfigure(3, weight=1)

        # 第一行：客户类型和地址类型
        ttk.Label(main_frame, text="客户类型:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.client_type = ttk.Combobox(main_frame, values=["wif"], state="readonly", width=15)
        self.client_type.set("wif")
        self.client_type.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 20))

        ttk.Label(main_frame, text="地址类型:").grid(row=0, column=2, sticky=tk.W, pady=5)
        self.address_type = ttk.Combobox(main_frame, values=["p2pkh"], state="readonly", width=15)
        self.address_type.set("p2pkh")
        self.address_type.grid(row=0, column=3, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))

        # 第二行：每秒生成数和查询线程数
        ttk.Label(main_frame, text="每秒生成数:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.gen_per_sec = tk.StringVar(value="10")
        gen_entry = ttk.Entry(main_frame, textvariable=self.gen_per_sec, width=15)
        gen_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 20))

        ttk.Label(main_frame, text="查询线程数:").grid(row=1, column=2, sticky=tk.W, pady=5)
        self.query_threads = tk.StringVar(value="5")
        query_entry = ttk.Entry(main_frame, textvariable=self.query_threads, width=15)
        query_entry.grid(row=1, column=3, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))

        # 第三行：查询间隔和查询超时
        ttk.Label(main_frame, text="查询间隔(秒):").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.query_interval = tk.StringVar(value="0.5")
        interval_entry = ttk.Entry(main_frame, textvariable=self.query_interval, width=15)
        interval_entry.grid(row=2, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 20))

        ttk.Label(main_frame, text="查询超时(秒):").grid(row=2, column=2, sticky=tk.W, pady=5)
        self.query_timeout = tk.StringVar(value="10")
        timeout_entry = ttk.Entry(main_frame, textvariable=self.query_timeout, width=15)
        timeout_entry.grid(row=2, column=3, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))

        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, columnspan=4, pady=20)

        # 按钮
        self.start_btn = ttk.Button(button_frame, text="开始生成和查询", command=self.start_mining)
        self.start_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.stop_btn = ttk.Button(button_frame, text="停止", command=self.stop_mining, state="disabled")
        self.stop_btn.pack(side=tk.LEFT, padx=(0, 10))

        clear_btn = ttk.Button(button_frame, text="清空结果", command=self.clear_results)
        clear_btn.pack(side=tk.LEFT)

        # 运行统计框架
        stats_frame = ttk.LabelFrame(main_frame, text="运行统计", padding="5")
        stats_frame.grid(row=4, column=0, columnspan=4, sticky=(tk.W, tk.E), pady=(10, 0))

        self.stats_label = ttk.Label(stats_frame, text="运行时间: 00:00:00 | 已生成: 0 (0.0/秒) | 已查询: 0 (0.0/秒) | 发现有余额: 0")
        self.stats_label.pack()

        # 当前查询状态
        self.status_label = ttk.Label(main_frame, text="正在查询 1GjGryaLd8cc81GSk6t... - 查询失败: 所有API查询失败，最后错误: Blockstream: 网络错误 - HT")
        self.status_label.grid(row=5, column=0, columnspan=4, pady=(10, 0), sticky=(tk.W, tk.E))

        # 有余额的地址记录框架
        log_frame = ttk.LabelFrame(main_frame, text="有余额的地址记录:", padding="5")
        log_frame.grid(row=6, column=0, columnspan=4, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)

        # 日志显示区域
        self.log_text = scrolledtext.ScrolledText(log_frame, height=10, width=80)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置主框架的行权重
        main_frame.rowconfigure(6, weight=1)

    def generate_private_key(self):
        """生成32字节的私钥"""
        return secrets.randbits(256).to_bytes(32, 'big')

    def private_key_to_wif(self, private_key, compressed=True):
        """将私钥转换为WIF格式"""
        # 添加网络字节（主网为0x80）
        extended_key = b'\x80' + private_key

        # 如果是压缩格式，添加0x01
        if compressed:
            extended_key += b'\x01'

        # 计算校验和
        checksum = hashlib.sha256(hashlib.sha256(extended_key).digest()).digest()[:4]

        # 组合并编码
        wif_key = extended_key + checksum
        return base58.b58encode(wif_key).decode('utf-8')

    def private_key_to_public_key(self, private_key, compressed=True):
        """从私钥生成公钥"""
        sk = SigningKey.from_string(private_key, curve=SECP256k1)
        vk = sk.get_verifying_key()

        if compressed:
            # 压缩公钥格式
            x = vk.pubkey.point.x()
            y = vk.pubkey.point.y()
            prefix = b'\x02' if y % 2 == 0 else b'\x03'
            return prefix + x.to_bytes(32, 'big')
        else:
            # 未压缩公钥格式
            return b'\x04' + vk.to_string()

    def public_key_to_p2pkh(self, public_key):
        """生成P2PKH地址"""
        # SHA256哈希
        sha256_hash = hashlib.sha256(public_key).digest()

        # RIPEMD160哈希
        ripemd160 = hashlib.new('ripemd160')
        ripemd160.update(sha256_hash)
        hash160 = ripemd160.digest()

        # 添加网络字节（主网P2PKH为0x00）
        versioned_hash = b'\x00' + hash160

        # 计算校验和
        checksum = hashlib.sha256(hashlib.sha256(versioned_hash).digest()).digest()[:4]

        # 组合并编码
        address_bytes = versioned_hash + checksum
        return base58.b58encode(address_bytes).decode('utf-8')

    def query_balance_api(self, address):
        """通过API查询地址余额"""
        apis = [
            {
                'name': 'Blockstream',
                'url': f'https://blockstream.info/api/address/{address}',
                'balance_key': 'chain_stats.funded_txo_sum'
            },
            {
                'name': 'BlockCypher',
                'url': f'https://api.blockcypher.com/v1/btc/main/addrs/{address}/balance',
                'balance_key': 'balance'
            },
            {
                'name': 'Blockchain.info',
                'url': f'https://blockchain.info/rawaddr/{address}',
                'balance_key': 'final_balance'
            }
        ]

        timeout = float(self.query_timeout.get())

        for api in apis:
            try:
                response = requests.get(api['url'], timeout=timeout)
                if response.status_code == 200:
                    data = response.json()

                    # 根据不同API获取余额
                    if api['name'] == 'Blockstream':
                        balance = data.get('chain_stats', {}).get('funded_txo_sum', 0)
                    else:
                        balance = data.get(api['balance_key'], 0)

                    return balance, api['name']

            except Exception as e:
                continue

        return None, "所有API查询失败"

    def start_mining(self):
        """开始挖矿"""
        if self.is_running:
            return

        self.is_running = True
        self.generated_count = 0
        self.checked_count = 0
        self.found_count = 0
        self.start_time = time.time()

        # 更新按钮状态
        self.start_btn.config(state="disabled")
        self.stop_btn.config(state="normal")

        # 启动工作线程
        self.worker_thread = threading.Thread(target=self.mining_worker, daemon=True)
        self.worker_thread.start()

        # 启动统计更新
        self.update_stats()

    def stop_mining(self):
        """停止挖矿"""
        self.is_running = False

        # 更新按钮状态
        self.start_btn.config(state="normal")
        self.stop_btn.config(state="disabled")

    def mining_worker(self):
        """挖矿工作线程"""
        try:
            gen_per_sec = int(self.gen_per_sec.get())
            query_threads_count = int(self.query_threads.get())
            query_interval = float(self.query_interval.get())

            # 创建查询队列
            self.query_queue = queue.Queue()

            # 启动查询线程
            for i in range(query_threads_count):
                query_thread = threading.Thread(target=self.query_worker, daemon=True)
                query_thread.start()

            # 生成密钥和地址
            while self.is_running:
                try:
                    # 生成私钥
                    private_key = self.generate_private_key()

                    # 生成WIF格式私钥
                    wif_key = self.private_key_to_wif(private_key)

                    # 生成公钥
                    public_key = self.private_key_to_public_key(private_key)

                    # 生成地址
                    address = self.public_key_to_p2pkh(public_key)

                    # 添加到查询队列
                    self.query_queue.put((wif_key, address))

                    self.generated_count += 1

                    # 控制生成速度
                    time.sleep(1.0 / gen_per_sec)

                except Exception as e:
                    self.log_error(f"生成密钥时出错: {str(e)}")

        except Exception as e:
            self.log_error(f"挖矿线程出错: {str(e)}")

    def query_worker(self):
        """查询工作线程"""
        query_interval = float(self.query_interval.get())

        while self.is_running:
            try:
                # 从队列获取地址
                wif_key, address = self.query_queue.get(timeout=1)

                # 更新状态显示
                self.root.after(0, lambda: self.status_label.config(
                    text=f"正在查询 {address[:15]}... - 查询中..."
                ))

                # 查询余额
                balance, api_name = self.query_balance_api(address)

                self.checked_count += 1

                if balance is not None and balance > 0:
                    # 发现有余额的地址
                    self.found_count += 1
                    message = f"发现有余额地址！\n私钥: {wif_key}\n地址: {address}\n余额: {balance} satoshi\nAPI: {api_name}"
                    self.log_queue.put(('found', message))

                # 等待间隔
                time.sleep(query_interval)

            except queue.Empty:
                continue
            except Exception as e:
                self.log_error(f"查询线程出错: {str(e)}")

    def process_log_queue(self):
        """处理日志队列"""
        while True:
            try:
                log_type, message = self.log_queue.get(timeout=1)

                if log_type == 'found':
                    # 在界面显示
                    self.root.after(0, lambda m=message: self.log_text.insert(tk.END, f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - {m}\n\n"))
                    self.root.after(0, lambda: self.log_text.see(tk.END))

                    # 写入日志文件
                    self.logger.info(message)

                elif log_type == 'error':
                    # 错误日志
                    self.root.after(0, lambda m=message: self.log_text.insert(tk.END, f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - 错误: {m}\n"))
                    self.root.after(0, lambda: self.log_text.see(tk.END))

                    # 写入日志文件
                    self.logger.error(message)

            except queue.Empty:
                continue
            except Exception as e:
                print(f"日志处理出错: {e}")

    def log_error(self, message):
        """记录错误"""
        self.log_queue.put(('error', message))

    def clear_results(self):
        """清空结果"""
        self.log_text.delete(1.0, tk.END)

    def update_stats(self):
        """更新统计信息"""
        if not self.is_running:
            return

        # 计算运行时间
        if self.start_time:
            elapsed = time.time() - self.start_time
            hours = int(elapsed // 3600)
            minutes = int((elapsed % 3600) // 60)
            seconds = int(elapsed % 60)
            time_str = f"{hours:02d}:{minutes:02d}:{seconds:02d}"

            # 计算速率
            gen_rate = self.generated_count / elapsed if elapsed > 0 else 0
            check_rate = self.checked_count / elapsed if elapsed > 0 else 0

            # 更新统计标签
            stats_text = f"运行时间: {time_str} | 已生成: {self.generated_count} ({gen_rate:.1f}/秒) | 已查询: {self.checked_count} ({check_rate:.1f}/秒) | 发现有余额: {self.found_count}"
            self.stats_label.config(text=stats_text)

        # 每秒更新一次
        self.root.after(1000, self.update_stats)

    def on_closing(self):
        """处理窗口关闭事件"""
        # 最小化到托盘而不是退出
        self.root.withdraw()

    def show_window(self, icon=None, item=None):
        """显示窗口"""
        self.root.deiconify()
        self.root.lift()

    def quit_application(self, icon=None, item=None):
        """退出应用程序"""
        self.is_running = False
        if hasattr(self, 'tray_icon'):
            self.tray_icon.stop()
        self.root.quit()
        self.root.destroy()


def main():
    root = tk.Tk()
    app = BitcoinMiner(root)
    root.mainloop()


if __name__ == "__main__":
    main()
