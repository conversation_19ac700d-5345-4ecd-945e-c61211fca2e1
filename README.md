# BitMiner - 比特币挖矿器

这是一个使用Python tkinter库编写的比特币密钥生成和余额查询程序。

## 功能特性

- **多线程并行处理**：密钥生成和API查询采用多线程，界面不会卡死
- **自动错误恢复**：网络错误或API失败时自动跳过，继续运行
- **系统托盘支持**：
  - 关闭窗口时最小化到托盘
  - 鼠标悬停显示运行信息
  - 双击托盘图标恢复窗口
  - 右键菜单：显示程序/退出程序
- **智能日志记录**：
  - 只记录有余额的地址和错误信息
  - 同时显示在界面和保存到log.txt文件
- **实时统计**：显示运行时间、生成速度、查询速度、发现数量
- **多API支持**：Blockstream、BlockCypher、Blockchain.info
- **可配置参数**：生成速度、查询线程数、查询间隔、超时时间

## 安装依赖

在运行程序之前，请安装所需的依赖包：

```bash
pip install -r requirements.txt
```

## 运行程序

```bash
python bitcoin_generator.py
```

## 使用说明

1. **客户类型**：私钥输出格式（目前支持WIF格式）

2. **地址类型**：比特币地址类型（目前支持P2PKH格式）

3. **每秒生成数**：控制密钥生成速度（建议10-50）

4. **查询线程数**：并行查询的线程数量（建议3-10）

5. **查询间隔**：每次查询之间的等待时间（秒，建议0.5-2）

6. **查询超时**：API查询超时时间（秒，建议5-15）

7. **开始生成和查询**：启动挖矿程序

8. **停止**：停止所有操作

9. **清空结果**：清空日志显示区域

## 托盘功能

- 点击窗口关闭按钮：程序最小化到系统托盘
- 鼠标悬停托盘图标：显示程序运行状态
- 双击托盘图标：恢复程序窗口
- 右键托盘图标：显示菜单（显示程序/退出程序）

## 注意事项

- **仅供学习研究使用**：此程序仅用于学习比特币技术原理
- **网络请求频率**：请合理设置查询参数，避免对API服务器造成过大压力
- **隐私安全**：程序生成的私钥是随机的，请注意保护隐私
- **法律合规**：请遵守当地法律法规，不要用于非法用途
- **资源消耗**：长时间运行会消耗网络流量和计算资源

## 技术实现

- **密钥生成**：使用 `secrets` 模块生成加密安全的随机私钥
- **椭圆曲线**：使用 `ecdsa` 库进行SECP256K1椭圆曲线计算
- **地址编码**：使用 `base58` 库进行Base58Check编码
- **哈希算法**：使用标准的SHA256和RIPEMD160哈希算法
- **多线程**：使用 `threading` 模块实现并发处理
- **网络请求**：使用 `requests` 库进行API查询
- **系统托盘**：使用 `pystray` 库实现托盘功能
- **日志记录**：使用 `logging` 模块记录重要信息

## API服务商

程序使用以下比特币API服务：
1. **Blockstream.info** - 主要API服务
2. **BlockCypher** - 备用API服务
3. **Blockchain.info** - 备用API服务

当一个API失败时，程序会自动尝试下一个API服务。
