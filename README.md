# 比特币密钥和地址生成器

这是一个使用Python tkinter库编写的比特币密钥和地址生成器程序。

## 功能特性

- 支持多种私钥格式：WIF、HEX
- 支持多种地址类型：P2PKH、P2SH、Bech32
- 可批量生成多个密钥对
- 图形化用户界面
- 结果显示和清空功能

## 安装依赖

在运行程序之前，请安装所需的依赖包：

```bash
pip install -r requirements.txt
```

## 运行程序

```bash
python bitcoin_generator.py
```

## 使用说明

1. **客户类型**：选择私钥的输出格式
   - WIF：钱包导入格式（Wallet Import Format）
   - HEX：十六进制格式

2. **地址类型**：选择生成的比特币地址类型
   - P2PKH：传统地址格式（以1开头）
   - P2SH：脚本哈希地址（以3开头）
   - Bech32：新格式地址（以bc1开头）

3. **生成数量**：输入要生成的密钥对数量（1-1000）

4. **生成密钥和地址**：点击按钮开始生成

5. **清空编辑**：清空结果显示区域

## 注意事项

- 此程序仅用于学习和测试目的
- 生成的私钥是随机的，请妥善保管
- 不要在生产环境中使用此程序生成的密钥
- 程序使用加密安全的随机数生成器

## 技术实现

- 使用 `secrets` 模块生成加密安全的随机私钥
- 使用 `ecdsa` 库进行椭圆曲线数字签名算法计算
- 使用 `base58` 库进行Base58编码
- 使用标准的SHA256和RIPEMD160哈希算法
